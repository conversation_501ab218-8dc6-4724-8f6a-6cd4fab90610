#!/bin/bash

# Vidur 模拟运行脚本
# 直接从配置文件加载所有参数并运行 vidur 模拟

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析 YAML 文件的函数 - 使用纯 bash，类似 run_vllm.sh 的方式
parse_yaml() {
    local file=$1
    local prefix=$2
    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ $line =~ ^[[:space:]]*# || $line =~ ^[[:space:]]*$ ]]; then
            continue
        fi
        # 匹配键值对
        if [[ $line =~ ^[[:space:]]*([^:]+):[[:space:]]*(.*)$ ]]; then
            key="${BASH_REMATCH[1]// /}"
            value="${BASH_REMATCH[2]}"
            # 移除行内注释（# 后面的内容）
            value="${value%%#*}"
            # 移除前后空格
            value="${value#"${value%%[![:space:]]*}"}"
            value="${value%"${value##*[![:space:]]}"}"
            # 移除引号
            value="${value#\"}"
            value="${value%\"}"
            # 只处理非空和非null值
            if [[ "$value" != "null" && "$value" != "" ]]; then
                declare -g "${prefix}_${key}=$value"
            fi
        fi
    done < "$file"
}

# 检查是否在正确的目录
if [ ! -f "vidur/main.py" ]; then
    print_error "vidur/main.py 未找到。请在 vidur 项目根目录运行此脚本。"
    exit 1
fi

# 检查配置文件
SHARED_CONFIG="validation/configs/config_shared.yaml"
VIDUR_CONFIG="validation/configs/config_vidur.yaml"

if [ ! -f "$SHARED_CONFIG" ]; then
    print_error "共享配置文件未找到: $SHARED_CONFIG"
    exit 1
fi

if [ ! -f "$VIDUR_CONFIG" ]; then
    print_error "Vidur 配置文件未找到: $VIDUR_CONFIG"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "未检测到虚拟环境。请确保已激活 Python 环境。"
    print_warning "可以使用以下命令激活:"
    print_warning "  - conda: mamba activate ./env"
    print_warning "  - venv: source .venv/bin/activate"
fi

print_info "开始运行 Vidur LLM 推理模拟..."
print_info "配置文件:"
print_info "  - 共享配置: $SHARED_CONFIG"
print_info "  - Vidur 配置: $VIDUR_CONFIG"

# 解析配置文件
print_info "解析配置文件..."
parse_yaml "$SHARED_CONFIG" "SHARED"
parse_yaml "$VIDUR_CONFIG" "VIDUR"

# 检查 trace 文件（需要从嵌套的 YAML 中手动提取）
TRACE_FILE=""
while IFS= read -r line; do
    if [[ $line =~ trace_file:[[:space:]]*\"?([^\"]+)\"? ]]; then
        TRACE_FILE="${BASH_REMATCH[1]}"
        break
    fi
done < "$VIDUR_CONFIG"

if [ -n "$TRACE_FILE" ] && [ ! -f "$TRACE_FILE" ]; then
    print_warning "跟踪文件未找到: $TRACE_FILE"
    print_warning "请确保已运行 vLLM 测试并生成了跟踪文件。"
fi

# 构建 vidur 命令参数
print_info "构建 vidur 命令参数..."

VIDUR_ARGS=""

# 模型配置 - 从共享配置
if [ ! -z "$SHARED_model_name" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_model_name $SHARED_model_name"
fi
# dtype, max_model_len, trust_remote_code, gpu_memory_utilization 等参数在 vidur 中不直接支持
# 这些是 vLLM 特定的参数

# 并行配置
if [ ! -z "$SHARED_tensor_parallel_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_tensor_parallel_size $SHARED_tensor_parallel_size"
fi
if [ ! -z "$SHARED_pipeline_parallel_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_num_pipeline_stages $SHARED_pipeline_parallel_size"
fi

# 设备配置
if [ ! -z "$SHARED_device" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_device $SHARED_device"
fi

# KV Cache 和调度配置通过调度器特定配置传递
# 这些参数不是 replica_config 的直接属性

# 从 VIDUR 配置中提取简单的键值对
if [ ! -z "$VIDUR_num_replicas" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --cluster_config_num_replicas $VIDUR_num_replicas"
fi

# 手动处理嵌套的配置项
# 集群配置
NUM_REPLICAS=""
while IFS= read -r line; do
    if [[ $line =~ num_replicas:[[:space:]]*([0-9]+) ]]; then
        NUM_REPLICAS="${BASH_REMATCH[1]}"
        break
    fi
done < "$VIDUR_CONFIG"
if [ ! -z "$NUM_REPLICAS" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --cluster_config_num_replicas $NUM_REPLICAS"
fi

# 副本配置
MEMORY_MARGIN=""
NUM_PIPELINE_STAGES=""
NETWORK_DEVICE=""
while IFS= read -r line; do
    if [[ $line =~ memory_margin_fraction:[[:space:]]*([0-9.]+) ]]; then
        MEMORY_MARGIN="${BASH_REMATCH[1]}"
    elif [[ $line =~ num_pipeline_stages:[[:space:]]*([0-9]+) ]]; then
        NUM_PIPELINE_STAGES="${BASH_REMATCH[1]}"
    elif [[ $line =~ network_device:[[:space:]]*\"?([^\"]+)\"? ]]; then
        NETWORK_DEVICE="${BASH_REMATCH[1]}"
    fi
done < "$VIDUR_CONFIG"

if [ ! -z "$MEMORY_MARGIN" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_memory_margin_fraction $MEMORY_MARGIN"
fi
if [ ! -z "$NUM_PIPELINE_STAGES" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_num_pipeline_stages $NUM_PIPELINE_STAGES"
fi
if [ ! -z "$NETWORK_DEVICE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_config_network_device $NETWORK_DEVICE"
fi

# 调度器配置
GLOBAL_SCHEDULER_TYPE=""
REPLICA_SCHEDULER_TYPE=""
WATERMARK_BLOCKS_FRACTION=""
while IFS= read -r line; do
    if [[ $line =~ type:[[:space:]]*\"?([^\"]+)\"? ]]; then
        if [[ "$prev_line" =~ global_scheduler_config ]]; then
            GLOBAL_SCHEDULER_TYPE="${BASH_REMATCH[1]}"
        elif [[ "$prev_line" =~ replica_scheduler_config ]]; then
            REPLICA_SCHEDULER_TYPE="${BASH_REMATCH[1]}"
        fi
    elif [[ $line =~ watermark_blocks_fraction:[[:space:]]*([0-9.]+) ]]; then
        WATERMARK_BLOCKS_FRACTION="${BASH_REMATCH[1]}"
    fi
    prev_line="$line"
done < "$VIDUR_CONFIG"

if [ ! -z "$GLOBAL_SCHEDULER_TYPE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --global_scheduler_config_type $GLOBAL_SCHEDULER_TYPE"
fi
if [ ! -z "$REPLICA_SCHEDULER_TYPE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --replica_scheduler_config_type $REPLICA_SCHEDULER_TYPE"
fi
if [ ! -z "$WATERMARK_BLOCKS_FRACTION" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_watermark_blocks_fraction $WATERMARK_BLOCKS_FRACTION"
fi

# 添加其他 vLLM 调度器特定配置
if [ ! -z "$SHARED_block_size" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_block_size $SHARED_block_size"
fi
if [ ! -z "$SHARED_max_num_seqs" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_batch_size_cap $SHARED_max_num_seqs"
fi
if [ ! -z "$SHARED_max_num_batched_tokens" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_max_tokens_in_batch $SHARED_max_num_batched_tokens"
fi
if [ ! -z "$SHARED_watermark_blocks_fraction" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --vllm_scheduler_config_watermark_blocks_fraction $SHARED_watermark_blocks_fraction"
fi

# 请求生成器配置
REQUEST_GENERATOR_TYPE=""
while IFS= read -r line; do
    if [[ $line =~ type:[[:space:]]*\"?([^\"]+)\"? ]] && [[ "$prev_line" =~ request_generator_config ]]; then
        REQUEST_GENERATOR_TYPE="${BASH_REMATCH[1]}"
        break
    fi
    prev_line="$line"
done < "$VIDUR_CONFIG"

if [ ! -z "$REQUEST_GENERATOR_TYPE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --request_generator_config_type $REQUEST_GENERATOR_TYPE"
fi

# 只有在使用 synthetic 生成器时才添加这些参数
if [ "$REQUEST_GENERATOR_TYPE" = "synthetic" ]; then
    if [ ! -z "$SHARED_num_requests" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --synthetic_request_generator_config_num_requests $SHARED_num_requests"
    fi
    if [ ! -z "$SHARED_seed" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --synthetic_request_generator_config_seed $SHARED_seed"
    fi
fi

# 处理 trace 请求生成器的配置
TRACE_PREFILL_SCALE_FACTOR=""
TRACE_DECODE_SCALE_FACTOR=""
TRACE_TIME_SCALE_FACTOR=""
TRACE_MAX_TOKENS=""
while IFS= read -r line; do
    if [[ $line =~ prefill_scale_factor:[[:space:]]*([0-9.]+) ]]; then
        TRACE_PREFILL_SCALE_FACTOR="${BASH_REMATCH[1]}"
    elif [[ $line =~ decode_scale_factor:[[:space:]]*([0-9.]+) ]]; then
        TRACE_DECODE_SCALE_FACTOR="${BASH_REMATCH[1]}"
    elif [[ $line =~ time_scale_factor:[[:space:]]*([0-9.]+) ]]; then
        TRACE_TIME_SCALE_FACTOR="${BASH_REMATCH[1]}"
    elif [[ $line =~ max_tokens:[[:space:]]*([0-9]+) ]]; then
        TRACE_MAX_TOKENS="${BASH_REMATCH[1]}"
    fi
done < "$VIDUR_CONFIG"

# 只有在使用 trace_replay 时才添加这些参数
if [ "$REQUEST_GENERATOR_TYPE" = "trace_replay" ]; then
    if [ ! -z "$TRACE_FILE" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_trace_file $TRACE_FILE"
    fi
    if [ ! -z "$TRACE_PREFILL_SCALE_FACTOR" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_prefill_scale_factor $TRACE_PREFILL_SCALE_FACTOR"
    fi
    if [ ! -z "$TRACE_DECODE_SCALE_FACTOR" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_decode_scale_factor $TRACE_DECODE_SCALE_FACTOR"
    fi
    if [ ! -z "$TRACE_TIME_SCALE_FACTOR" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_time_scale_factor $TRACE_TIME_SCALE_FACTOR"
    fi
    # max_tokens 使用 shared config 中的 max_model_len
    if [ ! -z "$SHARED_max_model_len" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_max_tokens $SHARED_max_model_len"
    elif [ ! -z "$TRACE_MAX_TOKENS" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --trace_request_generator_config_max_tokens $TRACE_MAX_TOKENS"
    fi
fi

# 间隔生成器配置
INTERVAL_GENERATOR_TYPE=""
while IFS= read -r line; do
    if [[ $line =~ type:[[:space:]]*\"?([^\"]+)\"? ]] && [[ "$prev_line" =~ interval_generator_config ]]; then
        INTERVAL_GENERATOR_TYPE="${BASH_REMATCH[1]}"
        break
    fi
    prev_line="$line"
done < "$VIDUR_CONFIG"

if [ ! -z "$INTERVAL_GENERATOR_TYPE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --interval_generator_config_type $INTERVAL_GENERATOR_TYPE"
fi
if [ ! -z "$SHARED_qps" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --poisson_request_interval_generator_config_qps $SHARED_qps"
fi

# 执行时间预测器配置
EXECUTION_TIME_PREDICTOR_TYPE=""
while IFS= read -r line; do
    if [[ $line =~ type:[[:space:]]*\"?([^\"]+)\"? ]] && [[ "$prev_line" =~ execution_time_predictor_config ]]; then
        EXECUTION_TIME_PREDICTOR_TYPE="${BASH_REMATCH[1]}"
        break
    fi
    prev_line="$line"
done < "$VIDUR_CONFIG"

if [ ! -z "$EXECUTION_TIME_PREDICTOR_TYPE" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --execution_time_predictor_config_type $EXECUTION_TIME_PREDICTOR_TYPE"
fi

# 添加执行时间预测器的关键参数
if [ "$EXECUTION_TIME_PREDICTOR_TYPE" = "random_forrest" ]; then
    # 从共享配置获取 max_model_len 用于预测器配置
    if [ ! -z "$SHARED_max_model_len" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size $SHARED_max_model_len"
        VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request $SHARED_max_model_len"
    fi
    # 从共享配置获取 max_num_seqs 用于批大小预测
    if [ ! -z "$SHARED_max_num_seqs" ]; then
        VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_prediction_max_batch_size $SHARED_max_num_seqs"
    fi
fi

# 指标配置
OUTPUT_DIR=""
CACHE_DIR=""
while IFS= read -r line; do
    if [[ $line =~ output_dir:[[:space:]]*\"?([^\"]+)\"? ]]; then
        OUTPUT_DIR="${BASH_REMATCH[1]}"
    elif [[ $line =~ cache_dir:[[:space:]]*\"?([^\"]+)\"? ]]; then
        CACHE_DIR="${BASH_REMATCH[1]}"
    fi
done < "$VIDUR_CONFIG"

if [ ! -z "$OUTPUT_DIR" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --metrics_config_output_dir $OUTPUT_DIR"
fi
if [ ! -z "$CACHE_DIR" ]; then
    VIDUR_ARGS="$VIDUR_ARGS --metrics_config_cache_dir $CACHE_DIR"
fi

# 布尔型指标配置
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_write_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_enable_chrome_trace"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_plots"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_request_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_batch_metrics"
VIDUR_ARGS="$VIDUR_ARGS --metrics_config_store_utilization_metrics"

# 添加 no-cache 选项防止使用损坏的缓存
VIDUR_ARGS="$VIDUR_ARGS --random_forrest_execution_time_predictor_config_no_cache"

# 运行 vidur 模拟
print_info "启动 Vidur 模拟..."
FULL_COMMAND="python -m vidur.main $VIDUR_ARGS"
print_info "命令: $FULL_COMMAND"

python -m vidur.main $VIDUR_ARGS

# 检查执行结果
if [ $? -eq 0 ]; then
    print_info "Vidur 模拟完成成功！"
    
    # 查找最新的输出目录
    if [ ! -z "$OUTPUT_DIR" ] && [ -d "$OUTPUT_DIR" ]; then
        print_info "输出目录: $OUTPUT_DIR"
        LATEST_OUTPUT=$(ls -t "$OUTPUT_DIR"/ 2>/dev/null | head -n1)
        if [ -n "$LATEST_OUTPUT" ]; then
            print_info "最新输出: $OUTPUT_DIR/$LATEST_OUTPUT"
        fi
    else
        # 检查默认的 simulator_output 目录
        if [ -d "simulator_output" ]; then
            LATEST_OUTPUT=$(ls -t simulator_output/ 2>/dev/null | head -n1)
            if [ -n "$LATEST_OUTPUT" ]; then
                print_info "最新输出目录: simulator_output/$LATEST_OUTPUT"
            fi
        fi
    fi
else
    print_error "Vidur 模拟失败，退出码: $?"
    exit 1
fi