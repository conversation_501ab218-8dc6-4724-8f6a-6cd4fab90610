#!/usr/bin/env python3
"""
预处理 vLLM 输出的 CSV 文件，转换为 Vidur 可以读取的格式
"""

import pandas as pd
import argparse
from pathlib import Path


def preprocess_vllm_csv(input_file: str, output_file: str = None):
    """
    将 vLLM 输出的 CSV 文件转换为 Vidur 期望的格式
    
    输入格式: request_id,arrived_at,num_prefill_tokens,num_decode_tokens,e2e_time
    输出格式: arrived_at,num_prefill_tokens,num_decode_tokens
    """
    
    # 读取 vLLM 输出文件
    df = pd.read_csv(input_file)
    
    print(f"读取 vLLM 文件: {input_file}")
    print(f"原始数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 检查必需的列是否存在
    required_columns = ['arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"缺少必需的列: {missing_columns}")
    
    # 只保留 Vidur 需要的列
    vidur_df = df[required_columns].copy()
    
    # 确保数据类型正确
    vidur_df['num_prefill_tokens'] = vidur_df['num_prefill_tokens'].astype(int)
    vidur_df['num_decode_tokens'] = vidur_df['num_decode_tokens'].astype(int)
    
    # 确保至少有1个token
    vidur_df['num_prefill_tokens'] = vidur_df['num_prefill_tokens'].clip(lower=1)
    vidur_df['num_decode_tokens'] = vidur_df['num_decode_tokens'].clip(lower=1)
    
    # 按到达时间排序
    vidur_df = vidur_df.sort_values('arrived_at').reset_index(drop=True)
    
    # 生成输出文件名
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_vidur_format.csv"
    
    # 保存转换后的文件
    vidur_df.to_csv(output_file, index=False)
    
    print(f"转换后数据形状: {vidur_df.shape}")
    print(f"输出文件: {output_file}")
    
    # 显示统计信息
    print("\n数据统计:")
    print(f"  时间范围: {vidur_df['arrived_at'].min():.3f} - {vidur_df['arrived_at'].max():.3f} 秒")
    print(f"  Prefill tokens: {vidur_df['num_prefill_tokens'].min()} - {vidur_df['num_prefill_tokens'].max()}")
    print(f"  Decode tokens: {vidur_df['num_decode_tokens'].min()} - {vidur_df['num_decode_tokens'].max()}")
    print(f"  平均 P/D 比例: {(vidur_df['num_prefill_tokens'] / vidur_df['num_decode_tokens']).mean():.2f}")
    
    return str(output_file)


def main():
    parser = argparse.ArgumentParser(description="预处理 vLLM CSV 文件为 Vidur 格式")
    parser.add_argument("input_file", help="vLLM 输出的 CSV 文件路径")
    parser.add_argument("-o", "--output", help="输出文件路径（可选）")
    
    args = parser.parse_args()
    
    try:
        output_file = preprocess_vllm_csv(args.input_file, args.output)
        print(f"\n✅ 转换成功！输出文件: {output_file}")
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
