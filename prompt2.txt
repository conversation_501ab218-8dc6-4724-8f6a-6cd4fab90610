vidur 是一个高保真的 LLM Serving 仿真平台。我现在想验证 vidur 是不是真的像它声称的那样高保真，想进行对比实验，因此我创建了一个 validation 目录，里面进行 vllm 和 vidur 的对比实验，实验的思路如下：
由于 vidur 实验的输入格式为：num_prefill_tokens 和 num_decode_tokens，这种格式 vllm 又无法运行，因此完整的实验链条如下：
1. 找到一个原始数据集 validation/datasets/context_values_unique.json ，用 vllm 运行目标模型（Llama3-8B）进行推理，得到 vidur 所需的 csv 文件（arrived_at, num_prefill_tokens, num_decode_tokens）
2. 将 vllm 中得到的 csv 文件传给 vidur 进行模拟测试得到 vidur 的模拟结果，最后和 vllm 里面得到的性能参数进行比对，检验 vidur 的保真度。
我目前已经基本实现好了各环节的代码（配置文件、运行脚本等），但是发现 vidur 测出来的 csv 文件有明显问题（validation/results/vidur 目录下的 csv 文件），一个最明显的就是 request_e2e_time 一直递增，这显然不对劲。你可以查看根目录下的 README 文件里的 vidur 运行命令，用那个命令跑出来的结果在 simulator_output 里面，那里面 request_e2e_time 就很正常。
现在我希望你：
1. 深入了解 vidur 项目（直接看源码或者使用 help 进行了解）
2. 检查 vllm 各个参数配置（查看源码 /share_data/users/yukaifeng/LLMServing/vidur/env/lib/python3.10/site-packages/vllm 或者联网搜索）
3. 检查目前 validation 目录下有哪些地方是有问题的（比如配置文件里的参数有没有问题，注意我有三个配置文件，一个共同参数配置文件，另外两个是特有配置文件。尽可能把参数都放到共享配置文件里面，因为我是要进行对比实验，要控制变量相同）
注：如果你需要使用 python 获取 help 等信息，请使用虚拟环境中的 python 解释器：./env/bin/python；**你先给出分析，我确认后再进行代码修改，不要直接进行代码修改！！！**